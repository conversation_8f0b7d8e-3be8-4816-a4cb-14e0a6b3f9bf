import styled from '@emotion/styled';
import {Flex} from 'antd';
import {useEffect} from 'react';
import {FetchMCPServersProvider} from '@/components/MCP/MCPServerCard/FetchMCPServersProvider';
import MCPServerList from '@/components/MCP/MCPServerCard/MCPServerList';
import MCPServerFilter, {FilterValues} from '@/components/MCP/MCPServerFilter';
import {apiGetAllZones, apiGetDefaultLabels} from '@/api/mcp';
import {ServerType, ServerProtocol, Order} from '@/components/MCP/MCPServerFilter/constant';
import {ALL_LABELS, OTHER_LABELS} from '@/components/MCP/MCPServerFilter/LabelsFilterContent';
import {SpaceLabel} from '@/types/mcp/mcp';
import RegionNavigation from './RegionNavigation';

const Container = styled(Flex)`
    width: 100%;
    height: calc(100vh - 48px);
    padding: 0 20px 16px;
    overflow: auto;
`;

const initialTabFormValue = {tab: 'all'};
const initialFilterFormValue: FilterValues = {
    serverSourceType: ServerType.ALL,
    serverProtocolType: ServerProtocol.ALL,
    labels: [-2],
    order: Order.DEFAULT,
};
const initialSearchParams = {
    ...initialTabFormValue,
    ...initialFilterFormValue,
};

const fetchSquareScenes = () => {
    return apiGetDefaultLabels().then(res => {
        return [
            {labelValue: '全部', id: ALL_LABELS, labelType: 'GLOABL'},
            ...res,
            {labelValue: '其他', id: OTHER_LABELS, labelType: 'GLOABL'},
        ];
    }).catch(error => {
        window.console.error(error);
        return [
            {labelValue: '全部', id: ALL_LABELS, labelType: 'GLOABL'},
            {labelValue: '其他', id: OTHER_LABELS, labelType: 'GLOABL'},
        ];
    }) as Promise<SpaceLabel[]>;
};

const MCPSquare = () => {
    useEffect(
        () => {
            apiGetAllZones();
        },
        []
    );
    return (
        <FetchMCPServersProvider initParams={initialSearchParams} platformType="hub" fetchScenes={fetchSquareScenes}>
            <Container vertical gap={16} id="scrollableDiv">
                <RegionNavigation />
                <MCPServerFilter
                    style={{marginTop: '16px', position: 'sticky', top: 0, zIndex: 2, backgroundColor: '#fff'}}
                    initialFilterFormValue={initialFilterFormValue}
                    initialTabFormValue={initialTabFormValue}
                />
                <MCPServerList scrollableTarget="scrollableDiv" />
            </Container>
        </FetchMCPServersProvider>

    );
};

export default MCPSquare;
